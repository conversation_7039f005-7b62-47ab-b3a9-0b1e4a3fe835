// Hamburger menu functionality
document.addEventListener('DOMContentLoaded', function() {
  const header = document.querySelector('.head header');
  const toggle = document.querySelector('.menu-toggle');
  const nav = document.querySelector('header nav');

  if (!header || !toggle || !nav) {
    console.log('Menu elements not found');
    return;
  }

  // Toggle menu function
  function toggleMenu() {
    const isOpen = header.classList.contains('menu-open');
    if (isOpen) {
      header.classList.remove('menu-open');
      toggle.setAttribute('aria-expanded', 'false');
      document.body.style.overflow = '';
    } else {
      header.classList.add('menu-open');
      toggle.setAttribute('aria-expanded', 'true');
      document.body.style.overflow = 'hidden';
    }
  }

  // Toggle menu on hamburger click
  toggle.addEventListener('click', function(e) {
    e.preventDefault();
    e.stopPropagation();
    toggleMenu();
  });

  // Close menu when clicking nav links
  nav.querySelectorAll('a').forEach(function(link) {
    link.addEventListener('click', function() {
      header.classList.remove('menu-open');
      toggle.setAttribute('aria-expanded', 'false');
      document.body.style.overflow = '';
    });
  });

  // Close menu when clicking outside
  document.addEventListener('click', function(e) {
    if (header.classList.contains('menu-open') &&
        !nav.contains(e.target) &&
        !toggle.contains(e.target)) {
      header.classList.remove('menu-open');
      toggle.setAttribute('aria-expanded', 'false');
      document.body.style.overflow = '';
    }
  });

  // Close menu on escape key
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape' && header.classList.contains('menu-open')) {
      header.classList.remove('menu-open');
      toggle.setAttribute('aria-expanded', 'false');
      document.body.style.overflow = '';
    }
  });
});

