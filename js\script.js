// Minimal hamburger toggle without changing other code
(function(){
  const header = document.querySelector('.head header');
  const toggle = document.querySelector('.menu-toggle');
  if(!header || !toggle) return;
  toggle.addEventListener('click', ()=>{
    const open = header.classList.toggle('menu-open');
    toggle.setAttribute('aria-expanded', open ? 'true' : 'false');
    // simple focus trap escape on ESC
  });
  // Close menu when clicking a nav link (mobile)
  header.querySelectorAll('nav a').forEach(a=>{
    a.addEventListener('click', ()=>{
      header.classList.remove('menu-open');
      toggle.setAttribute('aria-expanded','false');
    });
  });
})();

