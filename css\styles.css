/*
/* Hero Section with Background Image */
html,body{
    overflow-x: hidden;

    width: 100%;
}

/* hero-section */

.hero {
  background: url('../images/banner-bg.png') no-repeat center;
  background-size: cover;
}
@media(min-width:768px),(min-width:992px),(min-width:1200px){
.hero-img{
    padding-top:69px;
    margin-right: -12px;
}}

.nav-item a {
    font-weight: 600;
    font-size: 13px;
}
.nav-item a:hover {
    color: #13499f!important;
    text-decoration: none;
}
.btn.nav-demo{
    font-size: 13px;
    background-color: #13499f;
    color: white;
}

.head{
    position: relative;
    z-index: 4;

}

.harness h1{
    font-family: 'Poppins', 'Montserrat', 'Arial Black', sans-serif;
    padding-left:40px;
    color:white;
    font-size: 57px;
    font-weight: 700;
    margin-top: 195px;
    line-height: 65px;
}
.harness p{
    padding-left:40px;
    color: white;
    font-size: 18px;

}

/* welcome-section */
.welcome-section{
    padding: 50px 0px;
}
.welcome-img{
    position: relative;
}
.welcome-con h1{
    color:black;
    font-family: 'Poppin<PERSON>', 'Montserrat', 'Arial Black', sans-serif;
    font-weight: 700;
    font-size: 30px;
    line-height:45px;
}
.welcome-con h4{
    font-size: 22px;
    color: black;

}
.welcome-con p{
    font-size: 14px;
    line-height: 25px;
    color: black;
}
.small-1{
    position: absolute;
    top: 13px;
    right: 62px;
}
.small-2{
    position: absolute;
    top: 280px;
    right: 60px;

}

/* features-section */
.features-section{
    padding: 50px 0px;
    background-color: #13499f;
}
.fh{
    margin-top: 40px;
    font-family: 'Poppins', 'Montserrat', 'Arial Black', sans-serif;
    font-weight: 700;
    font-size: 30px;
    color: white;
    text-align: center;



}
.feature-card{
    margin-top: 35px;
    margin-bottom: 50px;
    padding: 30px;
    background-color: white;
    border-radius: 10px;
    min-height: 540px;
    font-size: 18px;
    color: black;

}
.feature-card p{
    font-size: 14px;
    font-weight: 495;
    line-height: 23px;
    color: black;
}
.feature-card h3{
    padding-top: 35px;
    font-size: 16px;
    font-weight: 700;
}

/* notification-section */
.notification-section{
    background-color: #bbebff;
    padding: 50px 0px;
}


.notify-text{
    margin-top: 140px;
}
.notify-text h3{
font-family: 'Poppins', 'Montserrat', 'Arial Black', sans-serif;
font-size: 30px;
font-weight: 700;
}
.notify-list {
    margin-top: 10px;
    }
.notify-item{
    display: flex;
    align-items: flex-start;
    margin-bottom: 10px;
    font-size: 1px;
    }
.notify-item img {
    margin-right: 8px;
    margin-top: 1px;
    }
.notify-item p{
    font-size: 14px;
    color: black;

}

/* seamless-section */
.seamless-section{
    background-color: white;
    padding: 70px 0px;
}
.seamless-text{
    margin-top: 140px;
}
.seamless-text h3{
    font-family: 'Poppins', 'Montserrat', 'Arial Black', sans-serif;
    font-size: 30px;
    font-weight: 700;
}
.seamless-list {
    margin-top: 10px;
    }
.seamless-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 10px;
    font-size: 15px;
    line-height: 23px;
    color: black;

    }
.seamless-item img {
    margin-right: 8px;
    margin-top: 1px;
    }


/*flexibility-section */
.flexibility-section{
    background-color: #13499f;

}

/* Left side - Full image */
.full-image{
    width: 100%;
    height: 100vh;
    object-fit: cover;
}

/* Right side - Center accordion */
.accordion-center{
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.flexibility-style{
    width: 80%;
    max-width: 500px;
}

.accordion-body{
    background-color:#13499f;
    color: white;
}

/* healthcare-section */
.healthcare-section{
    background-color: #beebfe;
    padding: 50px 0px;
}
.healthcare-text{
    margin-top: 140px;

}
.healthcare-text h3{
    font-family: 'Poppins','Montserrat','Arial Black',sans-serif;
    font-size: 30px;
    font-weight: 700;
    line-height: 47px;
}
.healthcare-text h4{
    font-size: 19px;
}

.healthcare-text p{
    font-size: 14px;
}

/* empower-section */
.empower-section{
    background-color:#13499f;
    padding: 50px 50px;
}
.empower-section h1{
    padding-top: 40px;
    color: white;
    font-family: 'Poppins','Montserrat','Arial Black',sans-serif;
    font-size: 30px;
    font-weight: 700;
    line-height: 45px;

}
.empower-para{
    color: white;
    font-size: 14px;
}
.empower-card{
    background-color: white;
    padding: 20px;
    border-radius: 10px;
    min-height: 254px;


}
.empower-card h4{
    font-size: 15px;
    font-weight: 10px;

}
.empower-card p{
    font-size: 12px;
    color: #39657D;
}

/* Form Container */
.form {
    background-color: #0c3c92;
    padding: 30px;
    border-radius: 10px;
}

/* Labels */
.form label {
    color: white;
    font-weight: 500;
    margin-bottom: 5px;
}

/* Input Fields */
.form .form-control {
    padding: 12px 15px;
    font-size: 15px;
    margin-bottom: 20px;
}
/* Submit Button */
.btn-form{
    background-color: #4dd0ff;
    color: black;
    font-size: 18px;
    font-weight: 600;
    border-radius: 6px;
    padding: 12px;
    width: 100%;
}

/* key-feature-section */
.key-feature-section{
    background-color: white;
    padding: 30px 0px;
    text-align: center;
}
.key-feature-section h1{
    font-family: 'Poppins','Montserrat','Arial Black',sans-serif;
    font-size: 30px;
    font-weight: 700;
    color: #0c3c92;
}
.key-feature-section p{
    padding-top: 10px;
    font-size: 15px;
    color: black;

}
/* footer */
.footer{
    background-color: #eff6fe;
    padding-top: 55px;
}
.footer-logo p{
    font-size: 14px;
    padding-top: 15px;
    margin-bottom: 25px;

}
.footer h5{
    color:#005cb9;
    font-size: 17px;
    font-weight: 700;
    padding-top: 16px;
}

.list-footer-Ql-2,.list-footer-Ql{
    list-style-image: url(../images/footer-icon.png);
    font-size: 14px;
    color: black;

}

.list-footer-Cu .ad, .list-footer-Cu .ml {
    position: relative;
    padding-left: 28px;
    font-size: 14px;
    color: black;
    line-height: 24px;
    margin-bottom: 8px;
    list-style: none;
}
.list-footer-Cu .ad::before {
    content: '';
    position: absolute;
    left: 0;
    top: 8%;
    width: 19px;
    height: 24px;
    background: url(../images/location-icon.png) no-repeat center;
    background-size: 24px;
}
.list-footer-Cu .ml::before {
    content: '';
    position: absolute;
    left: 0;
    top: 8%;
    width: 22px;
    height: 22px;
    background: url(../images/mail-icon.png) no-repeat center;
    background-size: 22px;
}

.footer-bottom {
    width: 100vw;
    background: #005cb9;
    color: #fff;
    padding: 20px;
    text-align: center;
    font-size: 14px;
}

/* =====================
   Responsive header (mobile)
   ===================== */
.menu-toggle{
    display:flex;
    background:transparent;
    border:0;
    width:36px; height:34px;
    padding:0;
    align-items:center; justify-content:center;
}
.menu-toggle .menu-bar{
    display:block;
    width:100%; height:3px;
    background:#13499f;
    margin:4px 0;
    border-radius:2px;
    transition:transform .3s ease, opacity .3s ease;
}

/* Keep header above other content */
.head header{ z-index: 1030; left:0; right:0; }
.logo-img{ max-height:40px; }

@media (max-width: 991.98px){
    /* Offcanvas style nav */
    header nav{ position:fixed; top:0; right:0; height:100vh; width:260px; background:#fff;
        box-shadow:-2px 0 16px rgba(0,0,0,.1); padding:80px 20px 20px; transform:translateX(100%);
        transition: transform .3s ease; }
    header.menu-open nav{ transform: translateX(0); }

    /* Stack menu items */
    header nav .nav{ flex-direction:column !important; align-items:flex-start; gap:6px; }
    header nav .nav .nav-link{ padding-left:0; font-size:16px; }

    /* Hide desktop CTA on small screens (kept in header unchanged) */
    .nav-demo{ display:none; }

    /* Mobile CTA button in drawer */
    .nav-demo-mobile{
        background-color:#13499f;
        color:#fff;
        font-weight:600;
        font-size:14px;
        padding:12px 20px;
        border:0;
        border-radius:6px;
    }
}

@media (min-width: 992px){
    /* Hide hamburger on desktop */
    .menu-toggle{ display:none; }

    /* Hide mobile CTA on desktop */
    .nav-demo-mobile{ display:none; }
}

/* Tiny screens: adjust hero text spacing */
@media (max-width: 767.98px){
    .harness h1{ padding-left:20px; font-size:34px; line-height:42px; margin-top:120px; }
    .harness p{ padding-left:20px; font-size:14px; }
}
